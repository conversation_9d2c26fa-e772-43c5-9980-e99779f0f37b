// Netlify function for syncing transportation reservations between local storage and Supabase
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
// Using service role key to bypass RLS policies
const supabaseUrl = process.env.SUPABASE_URL || 'https://vwpyvjoycnrgmpatzqzk.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ3cHl2am95Y25yZ21wYXR6cXprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDExMzk0MTksImV4cCI6MjA1NjcxNTQxOX0.sG7amfnd8UTMv3zzmaa3KKyPRTzW70cDW9VVcUCSq4c';
const supabase = createClient(supabaseUrl, supabaseKey);

// CORS headers
const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Content-Type': 'application/json'
};

exports.handler = async (event, context) => {
  // Handle preflight OPTIONS request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({ message: 'Preflight request successful' })
    };
  }

  // Only allow POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    // Parse request body
    const requestBody = JSON.parse(event.body);
    const { user_id, reservations } = requestBody;

    // Validate required parameters
    if (!user_id || !reservations || !Array.isArray(reservations)) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Missing required parameters: user_id and reservations array' })
      };
    }

    // Get existing reservations for this user from Supabase
    const { data: existingReservations, error: fetchError } = await supabase
      .from('transportation_reservations')
      .select('*')
      .eq('user_id', user_id);

    if (fetchError) {
      console.error('Error fetching existing reservations:', fetchError);
      
      // Check if the error is because the table doesn't exist
      if (fetchError.code === '42P01') {
        console.log('Table transportation_reservations does not exist, creating it...');
        
        // Try to create the table directly with SQL
        const { error: sqlError } = await supabase.rpc('exec_sql', {
          sql_query: `
            CREATE TABLE IF NOT EXISTS transportation_reservations (
              id SERIAL PRIMARY KEY,
              user_id TEXT NOT NULL,
              reservation_id TEXT NOT NULL,
              type TEXT NOT NULL,
              from_location TEXT NOT NULL,
              to_location TEXT NOT NULL,
              departure_date DATE NOT NULL,
              departure_time TIME,
              carrier TEXT,
              reference TEXT,
              notes TEXT,
              trip_type TEXT DEFAULT 'one-way',
              created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
            
            CREATE INDEX IF NOT EXISTS idx_transportation_reservations_user_id ON transportation_reservations(user_id);
            CREATE INDEX IF NOT EXISTS idx_transportation_reservations_reservation_id ON transportation_reservations(reservation_id);
            
            ALTER TABLE transportation_reservations ENABLE ROW LEVEL SECURITY;
            
            CREATE POLICY select_own_reservations ON transportation_reservations
              FOR SELECT USING (auth.uid()::text = user_id);
            
            CREATE POLICY insert_own_reservations ON transportation_reservations
              FOR INSERT WITH CHECK (auth.uid()::text = user_id);
            
            CREATE POLICY update_own_reservations ON transportation_reservations
              FOR UPDATE USING (auth.uid()::text = user_id);
            
            CREATE POLICY delete_own_reservations ON transportation_reservations
              FOR DELETE USING (auth.uid()::text = user_id);
          `
        });
        
        if (sqlError) {
          console.error('Error creating SQL function:', sqlError);
          return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Failed to create transportation_reservations table', message: sqlError.message })
          };
        }
      } else {
        return {
          statusCode: 500,
          headers,
          body: JSON.stringify({ error: 'Failed to fetch existing reservations', message: fetchError.message })
        };
      }
    }

    // Create a map of existing reservations by reservation_id for quick lookup
    const existingReservationsMap = {};
    if (existingReservations && existingReservations.length > 0) {
      existingReservations.forEach(reservation => {
        existingReservationsMap[reservation.reservation_id] = reservation;
      });
    }

    // Process each reservation from the client
    const results = [];
    for (const reservation of reservations) {
      // Ensure each reservation has the user_id
      reservation.user_id = user_id;

      // Convert reservation properties to database format
      const dbReservation = {
        user_id: reservation.user_id,
        reservation_id: reservation.id,
        type: reservation.type,
        from_location: reservation.from,
        to_location: reservation.to,
        departure_date: reservation.date,
        departure_time: reservation.time || null,
        carrier: reservation.carrier || null,
        reference: reservation.reference || null,
        notes: reservation.notes || null,
        trip_type: reservation.tripType || 'one-way'
      };

      // Check if this reservation already exists
      if (existingReservationsMap[reservation.id]) {
        // Update existing reservation
        const { data, error } = await supabase
          .from('transportation_reservations')
          .update(dbReservation)
          .eq('user_id', user_id)
          .eq('reservation_id', reservation.id)
          .select();

        if (error) {
          console.error('Error updating reservation:', error);
          results.push({ error: error.message, reservation });
        } else {
          results.push({ success: true, reservation: data[0], action: 'updated' });
        }
      } else {
        // Insert new reservation
        const { data, error } = await supabase
          .from('transportation_reservations')
          .insert([dbReservation])
          .select();

        if (error) {
          console.error('Error inserting reservation:', error);
          results.push({ error: error.message, reservation });
        } else {
          results.push({ success: true, reservation: data[0], action: 'inserted' });
        }
      }
    }

    // Check for reservations in the database that are not in the client's list
    // These should be deleted as they've been removed on the client
    if (existingReservations && existingReservations.length > 0) {
      const clientReservationIds = new Set(reservations.map(r => r.id));
      const reservationsToDelete = existingReservations.filter(r => !clientReservationIds.has(r.reservation_id));

      if (reservationsToDelete.length > 0) {
        for (const reservationToDelete of reservationsToDelete) {
          const { error } = await supabase
            .from('transportation_reservations')
            .delete()
            .eq('user_id', user_id)
            .eq('reservation_id', reservationToDelete.reservation_id);

          if (error) {
            console.error('Error deleting reservation:', error);
            results.push({ error: error.message, reservation: reservationToDelete, action: 'delete_failed' });
          } else {
            results.push({ success: true, reservation_id: reservationToDelete.reservation_id, action: 'deleted' });
          }
        }
      }
    }

    // Return successful response with results
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        message: 'Reservations synced successfully',
        results
      })
    };
  } catch (error) {
    console.error('Error in sync-transportation-reservations function:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Internal server error',
        message: error.message
      })
    };
  }
};
